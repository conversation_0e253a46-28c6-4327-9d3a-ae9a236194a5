"""
Database session utilities for the PI Lawyer AI system.

This module provides functions for creating and managing database sessions.
"""

import logging
import os
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine

# Set up logging
logger = logging.getLogger(__name__)

# Get database connection string from environment variables
# Try Supabase first, then fall back to local PostgreSQL
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")

if SUPABASE_URL and SUPABASE_SERVICE_KEY:
    # Use Supabase connection
    # Convert Supabase URL to PostgreSQL connection string
    # Supabase URL format: https://xxx.supabase.co
    # PostgreSQL URL format: postgresql+asyncpg://postgres:[password]@db.xxx.supabase.co:5432/postgres
    supabase_host = SUPABASE_URL.replace("https://", "").replace("http://", "")
    DATABASE_URL = f"postgresql+asyncpg://postgres:{SUPABASE_SERVICE_KEY}@db.{supabase_host}:5432/postgres"
    logger.info("Using Supabase database connection")
else:
    # Fall back to local PostgreSQL
    DB_USER = os.getenv("DB_USER", "postgres")
    DB_PASSWORD = os.getenv("DB_PASSWORD", "postgres")
    DB_HOST = os.getenv("DB_HOST", "localhost")
    DB_PORT = os.getenv("DB_PORT", "5432")
    DB_NAME = os.getenv("DB_NAME", "ailex")
    DATABASE_URL = f"postgresql+asyncpg://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    logger.info("Using local PostgreSQL database connection")

# Create async engine
engine = create_async_engine(
    DATABASE_URL,
    echo=False,  # Set to True for SQL query logging
    future=True,
)

# Create async session factory
async_session_factory = async_sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Get a database session.
    
    This function is used as a FastAPI dependency to provide database sessions
    to route handlers.
    
    Yields:
        AsyncSession: A database session
    """
    async with async_session_factory() as session:
        try:
            yield session
        finally:
            await session.close()


@asynccontextmanager
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Get a database session as an async context manager.
    
    This function is used for database operations outside of FastAPI routes.
    
    Yields:
        AsyncSession: A database session
    """
    async with async_session_factory() as session:
        try:
            yield session
        finally:
            await session.close()
